<?php

namespace Database\Seeders;

use App\Models\ExpenseType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ExpenseTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $expenseTypes = [
            [
                'name' => 'Maintenance',
                'description' => 'Regular building maintenance and repairs',
            ],
            [
                'name' => 'Utilities',
                'description' => 'Common area utilities (water, electricity, etc.)',
            ],
            [
                'name' => 'Security',
                'description' => 'Building security services and equipment',
            ],
        ];

        foreach ($expenseTypes as $type) {
            ExpenseType::create($type);
        }
    }
}
