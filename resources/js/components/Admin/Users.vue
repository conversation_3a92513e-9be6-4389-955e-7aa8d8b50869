<template>
  <div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
      <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
        <div class="p-6 bg-white border-b border-gray-200">
          <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-semibold text-gray-800">Users Management</h2>
            <button @click="showCreateModal = true" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
              Add New User
            </button>
          </div>

          <div v-if="message" class="mb-4 p-4 rounded" :class="messageType === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'">
            {{ message }}
          </div>

          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="user in users" :key="user.id">
                  <td class="px-6 py-4 whitespace-nowrap">{{ user.name }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">{{ user.email }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">{{ formatDate(user.created_at) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button @click="editUser(user)" class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</button>
                    <button @click="deleteUser(user)" class="text-red-600 hover:text-red-900">Delete</button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Create/Edit Modal -->
          <div v-if="showCreateModal || showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div class="mt-3">
                <h3 class="text-lg font-medium leading-6 text-gray-900 mb-4">
                  {{ showEditModal ? 'Edit User' : 'Create User' }}
                </h3>
                <form @submit.prevent="showEditModal ? updateUser() : createUser()" class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Name</label>
                    <input v-model="form.name" type="text" required
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Email</label>
                    <input v-model="form.email" type="email" required
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Password</label>
                    <input v-model="form.password" type="password" :required="!showEditModal"
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Confirm Password</label>
                    <input v-model="form.password_confirmation" type="password" :required="!showEditModal"
                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                  </div>
                  <div class="flex justify-end space-x-3">
                    <button type="button" @click="closeModal"
                      class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                      Cancel
                    </button>
                    <button type="submit"
                      class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                      {{ showEditModal ? 'Update' : 'Create' }}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      users: [],
      showCreateModal: false,
      showEditModal: false,
      message: '',
      messageType: 'success',
      form: {
        name: '',
        email: '',
        password: '',
        password_confirmation: '',
      },
      editingUser: null
    }
  },
  created() {
    this.fetchUsers()
  },
  methods: {
    async fetchUsers() {
      try {
        const response = await this.$axios.get('/admin/users')
        this.users = response.data.data
      } catch (error) {
        this.showMessage('Error fetching users', 'error')
      }
    },
    async createUser() {
      try {
        await this.$axios.post('/admin/users', this.form)
        this.showMessage('User created successfully')
        this.closeModal()
        this.fetchUsers()
      } catch (error) {
        this.showMessage(error.response?.data?.message || 'Error creating user', 'error')
      }
    },
    async updateUser() {
      try {
        await this.$axios.put(`/admin/users/${this.editingUser.id}`, this.form)
        this.showMessage('User updated successfully')
        this.closeModal()
        this.fetchUsers()
      } catch (error) {
        this.showMessage(error.response?.data?.message || 'Error updating user', 'error')
      }
    },
    async deleteUser(user) {
      if (!confirm('Are you sure you want to delete this user?')) return
      
      try {
        await this.$axios.delete(`/admin/users/${user.id}`)
        this.showMessage('User deleted successfully')
        this.fetchUsers()
      } catch (error) {
        this.showMessage(error.response?.data?.message || 'Error deleting user', 'error')
      }
    },
    editUser(user) {
      this.editingUser = user
      this.form = {
        name: user.name,
        email: user.email,
        password: '',
        password_confirmation: ''
      }
      this.showEditModal = true
    },
    closeModal() {
      this.showCreateModal = false
      this.showEditModal = false
      this.editingUser = null
      this.form = {
        name: '',
        email: '',
        password: '',
        password_confirmation: ''
      }
    },
    showMessage(message, type = 'success') {
      this.message = message
      this.messageType = type
      setTimeout(() => {
        this.message = ''
      }, 3000)
    },
    formatDate(date) {
      return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }
  }
}
</script> 