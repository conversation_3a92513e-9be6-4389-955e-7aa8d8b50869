<template>
  <form @submit.prevent="handleSubmit" class="space-y-6">
    <div>
      <label for="expense_type" class="block text-sm font-medium text-gray-700">Expense Type</label>
      <select
        id="expense_type"
        v-model="formData.expense_type_id"
        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
        required
      >
        <option value="">Select Type</option>
        <option v-for="type in expenseTypes" :key="type.id" :value="type.id">
          {{ type.name }}
        </option>
      </select>
    </div>

    <div>
      <label for="month" class="block text-sm font-medium text-gray-700">Month</label>
      <select
        id="month"
        v-model="formData.month"
        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
        required
      >
        <option value="">Select Month</option>
        <option value="01">January</option>
        <option value="02">February</option>
        <option value="03">March</option>
        <option value="04">April</option>
        <option value="05">May</option>
        <option value="06">June</option>
        <option value="07">July</option>
        <option value="08">August</option>
        <option value="09">September</option>
        <option value="10">October</option>
        <option value="11">November</option>
        <option value="12">December</option>
      </select>
    </div>

    <div>
      <label for="year" class="block text-sm font-medium text-gray-700">Year</label>
      <input
        type="number"
        id="year"
        v-model="formData.year"
        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
        min="2023"
        max="2030"
        required
      />
    </div>

    <div>
      <label for="due_date" class="block text-sm font-medium text-gray-700">Due Date</label>
      <input
        type="date"
        id="due_date"
        v-model="formData.due_date"
        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
        required
      />
    </div>

    <div>
      <label for="amount" class="block text-sm font-medium text-gray-700">Amount</label>
      <div class="mt-1 relative rounded-md shadow-sm">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <span class="text-gray-500 sm:text-sm">$</span>
        </div>
        <input
          type="number"
          id="amount"
          v-model="formData.amount"
          class="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md"
          placeholder="0.00"
          step="0.01"
          min="0"
          required
        />
      </div>
    </div>

    <div>
      <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
      <textarea
        id="notes"
        v-model="formData.notes"
        rows="3"
        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
        placeholder="Enter expense notes..."
      ></textarea>
    </div>

    <div>
      <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
      <select
        id="status"
        v-model="formData.status"
        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
        required
      >
        <option value="pending">Pending</option>
        <option value="paid">Paid</option>
        <option value="overdue">Overdue</option>
      </select>
    </div>

    <div class="flex justify-end space-x-3">
      <button
        type="button"
        @click="$emit('cancel')"
        class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      >
        Cancel
      </button>
      <button
        type="submit"
        :disabled="processing"
        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      >
        {{ processing ? 'Saving...' : (isEdit ? 'Update Expense' : 'Create Expense') }}
      </button>
    </div>
  </form>
</template>

<script>
export default {
  name: 'ExpenseForm',
  props: {
    expense: {
      type: Object,
      default: () => ({
        expense_type_id: '',
        month: '',
        year: new Date().getFullYear(),
        amount: '',
        due_date: '',
        notes: '',
        status: 'pending'
      })
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      processing: false,
      expenseTypes: [],
      formData: {
        expense_type_id: this.expense.expense_type_id || '',
        month: this.expense.month || '',
        year: this.expense.year || new Date().getFullYear(),
        amount: this.expense.amount || '',
        due_date: this.expense.due_date || '',
        notes: this.expense.notes || '',
        status: this.expense.status || 'pending'
      }
    };
  },
  created() {
    this.fetchExpenseTypes();
  },
  methods: {
    async fetchExpenseTypes() {
      try {
        const response = await this.$axios.get('/expense-types');
        this.expenseTypes = response.data;
      } catch (error) {
        console.error('Error fetching expense types:', error);
      }
    },
    async handleSubmit() {
      this.processing = true;
      try {
        const response = await this.$axios[this.isEdit ? 'put' : 'post'](
          this.isEdit ? `/expenses/${this.expense.id}` : '/expenses',
          this.formData
        );

        this.$emit('success', response.data);
      } catch (error) {
        this.$emit('error', error.response?.data?.message || 'Failed to save expense');
      } finally {
        this.processing = false;
      }
    }
  }
};
</script> 