<template>
  <form @submit.prevent="handleSubmit" class="space-y-6">
    <div>
      <label for="expense_type" class="block text-sm font-medium text-gray-700">نوع المصروف</label>
      <select
        id="expense_type"
        v-model="formData.expense_type"
        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
        required
      >
        <option value="">اختر النوع</option>
        <option value="building_services">خدمات عمارة</option>
        <option value="building_electricity">كهرباء عمارة</option>
        <option value="personal_electricity">كهرباء منزلي</option>
        <option value="water">مياه</option>
        <option value="other">أخرى</option>
      </select>
    </div>

    <div>
      <label for="month" class="block text-sm font-medium text-gray-700">الشهر</label>
      <select
        id="month"
        v-model="formData.month"
        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
        required
      >
        <option value="">اختر الشهر</option>
        <option value="01">يناير</option>
        <option value="02">فبراير</option>
        <option value="03">مارس</option>
        <option value="04">أبريل</option>
        <option value="05">مايو</option>
        <option value="06">يونيو</option>
        <option value="07">يوليو</option>
        <option value="08">أغسطس</option>
        <option value="09">سبتمبر</option>
        <option value="10">أكتوبر</option>
        <option value="11">نوفمبر</option>
        <option value="12">ديسمبر</option>
      </select>
    </div>

    <div>
      <label for="year" class="block text-sm font-medium text-gray-700">السنة</label>
      <input
        type="number"
        id="year"
        v-model="formData.year"
        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
        min="2023"
        max="2025"
        required
      />
    </div>

    <div>
      <label for="amount" class="block text-sm font-medium text-gray-700">المبلغ</label>
      <div class="mt-1 relative rounded-md shadow-sm">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <span class="text-gray-500 sm:text-sm">$</span>
        </div>
        <input
          type="number"
          id="amount"
          v-model="formData.amount"
          class="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md"
          placeholder="0.00"
          step="0.01"
          min="0"
          required
        />
      </div>
    </div>

    <div>
      <label for="description" class="block text-sm font-medium text-gray-700">الوصف</label>
      <textarea
        id="description"
        v-model="formData.description"
        rows="3"
        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
        placeholder="أدخل وصف المصروف..."
      ></textarea>
    </div>

    <div>
      <label for="status" class="block text-sm font-medium text-gray-700">الحالة</label>
      <select
        id="status"
        v-model="formData.status"
        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
        required
      >
        <option value="pending">معلق</option>
        <option value="paid">مدفوع</option>
        <option value="overdue">متأخر</option>
      </select>
    </div>

    <div class="flex justify-end space-x-3">
      <button
        type="button"
        @click="$emit('cancel')"
        class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      >
        إلغاء
      </button>
      <button
        type="submit"
        :disabled="processing"
        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      >
        {{ processing ? 'جاري الحفظ...' : (isEdit ? 'تحديث المصروف' : 'إنشاء مصروف') }}
      </button>
    </div>
  </form>
</template>

<script>
export default {
  name: 'ExpenseForm',
  props: {
    expense: {
      type: Object,
      default: () => ({
        expense_type: '',
        month: '',
        year: new Date().getFullYear(),
        amount: '',
        description: '',
        status: 'pending'
      })
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      processing: false,
      formData: {
        expense_type: this.expense.expense_type || '',
        month: this.expense.month || '',
        year: this.expense.year || new Date().getFullYear(),
        amount: this.expense.amount || '',
        description: this.expense.description || '',
        status: this.expense.status || 'pending'
      }
    };
  },
  methods: {
    async handleSubmit() {
      this.processing = true;
      try {
        const response = await this.$axios[this.isEdit ? 'put' : 'post'](
          this.isEdit ? `/expenses/${this.expense.id}` : '/expenses',
          this.formData
        );
        
        this.$emit('success', response.data);
      } catch (error) {
        this.$emit('error', error.response?.data?.message || 'فشل في حفظ المصروف');
      } finally {
        this.processing = false;
      }
    }
  }
};
</script> 