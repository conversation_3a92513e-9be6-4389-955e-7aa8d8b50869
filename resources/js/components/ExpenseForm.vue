<template>
  <div class="bg-white p-6 rounded-lg shadow-sm">
    <h2 class="text-xl font-semibold text-gray-900 mb-6">Create New Expense</h2>

    <form @submit.prevent="handleSubmit" class="space-y-6">
      <div>
        <label for="expense_type" class="block text-sm font-medium text-gray-700 mb-2">Expense Type</label>
        <select
          id="expense_type"
          v-model="formData.expense_type_id"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        >
          <option value="">Select Type</option>
          <option v-for="type in expenseTypes" :key="type.id" :value="type.id">
            {{ type.name }}
          </option>
        </select>
      </div>

      <div>
        <label for="user" class="block text-sm font-medium text-gray-700 mb-2">Neighbor</label>
        <select
          id="user"
          v-model="formData.user_id"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        >
          <option value="">Select Neighbor</option>
          <option v-for="user in neighbors" :key="user.id" :value="user.id">
            {{ user.name }} (Apt: {{ user.apartment_number }})
          </option>
        </select>
      </div>

      <div>
        <label for="month" class="block text-sm font-medium text-gray-700 mb-2">Month</label>
        <select
          id="month"
          v-model="formData.month"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        >
          <option value="">Select Month</option>
          <option value="01">January</option>
          <option value="02">February</option>
          <option value="03">March</option>
          <option value="04">April</option>
          <option value="05">May</option>
          <option value="06">June</option>
          <option value="07">July</option>
          <option value="08">August</option>
          <option value="09">September</option>
          <option value="10">October</option>
          <option value="11">November</option>
          <option value="12">December</option>
        </select>
      </div>

      <div>
        <label for="year" class="block text-sm font-medium text-gray-700 mb-2">Year</label>
        <input
          type="number"
          id="year"
          v-model="formData.year"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          min="2023"
          max="2030"
          required
        />
      </div>

      <div>
        <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">Amount</label>
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span class="text-gray-500 text-sm">$</span>
          </div>
          <input
            type="number"
            id="amount"
            v-model="formData.amount"
            class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="0.00"
            step="0.01"
            min="0"
            required
          />
        </div>
      </div>

      <div>
        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
        <textarea
          id="notes"
          v-model="formData.notes"
          rows="4"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
          placeholder="Enter expense notes..."
        ></textarea>
      </div>

      <div>
        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
        <select
          id="status"
          v-model="formData.status"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          required
        >
          <option value="pending">Pending</option>
          <option value="paid">Paid</option>
          <option value="overdue">Overdue</option>
        </select>
      </div>

      <div class="flex items-center">
        <input
          type="checkbox"
          id="is_automatic"
          v-model="formData.is_automatic"
          class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
        />
        <label for="is_automatic" class="ml-2 text-sm text-gray-700">
          Automatic Monthly Expense
        </label>
      </div>

      <div class="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          @click="$emit('cancel')"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          :disabled="processing"
          class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ processing ? 'Saving...' : (isEdit ? 'Update Expense' : 'Create Expense') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
export default {
  name: 'ExpenseForm',
  props: {
    expense: {
      type: Object,
      default: () => ({
        expense_type_id: '',
        user_id: '',
        month: '',
        year: new Date().getFullYear(),
        amount: '',
        notes: '',
        status: 'pending',
        is_automatic: false
      })
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      processing: false,
      expenseTypes: [],
      neighbors: [],
      formData: {
        expense_type_id: this.expense.expense_type_id || '',
        user_id: this.expense.user_id || '',
        month: this.expense.month || '',
        year: this.expense.year || new Date().getFullYear(),
        amount: this.expense.amount || '',
        notes: this.expense.notes || '',
        status: this.expense.status || 'pending',
        is_automatic: this.expense.is_automatic || false
      }
    };
  },
  created() {
    this.fetchExpenseTypes();
    this.fetchNeighbors();
  },
  methods: {
    async fetchExpenseTypes() {
      try {
        const response = await this.$axios.get('/expense-types');
        this.expenseTypes = response.data;
      } catch (error) {
        console.error('Error fetching expense types:', error);
      }
    },
    async fetchNeighbors() {
      try {
        const response = await this.$axios.get('/admin/users');
        this.neighbors = response.data.data.filter(user => user.role === 'neighbor');
      } catch (error) {
        console.error('Error fetching neighbors:', error);
      }
    },
    async handleSubmit() {
      this.processing = true;
      try {
        const response = await this.$axios[this.isEdit ? 'put' : 'post'](
          this.isEdit ? `/expenses/${this.expense.id}` : '/expenses',
          this.formData
        );

        this.$emit('success', response.data);
      } catch (error) {
        this.$emit('error', error.response?.data?.message || 'Failed to save expense');
      } finally {
        this.processing = false;
      }
    }
  }
};
</script> 