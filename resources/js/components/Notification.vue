<template>
  <div
    v-if="show"
    class="fixed top-4 right-4 z-50 max-w-sm w-full bg-white rounded-lg shadow-lg overflow-hidden"
    :class="typeClasses"
  >
    <div class="p-4">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <component
            :is="icon"
            class="h-6 w-6"
            :class="iconClasses"
            aria-hidden="true"
          />
        </div>
        <div class="ml-3 w-0 flex-1">
          <p class="text-sm font-medium" :class="textClasses">
            {{ title }}
          </p>
          <p class="mt-1 text-sm" :class="textClasses">
            {{ message }}
          </p>
        </div>
        <div class="ml-4 flex-shrink-0 flex">
          <button
            @click="close"
            class="inline-flex text-gray-400 hover:text-gray-500 focus:outline-none"
          >
            <span class="sr-only">Close</span>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path
                fill-rule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clip-rule="evenodd"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Notification',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'info',
      validator: value => ['success', 'error', 'warning', 'info'].includes(value)
    },
    title: {
      type: String,
      required: true
    },
    message: {
      type: String,
      required: true
    },
    duration: {
      type: Number,
      default: 5000
    }
  },
  data() {
    return {
      timer: null
    };
  },
  computed: {
    typeClasses() {
      const classes = {
        success: 'border-l-4 border-green-400',
        error: 'border-l-4 border-red-400',
        warning: 'border-l-4 border-yellow-400',
        info: 'border-l-4 border-blue-400'
      };
      return classes[this.type];
    },
    iconClasses() {
      const classes = {
        success: 'text-green-400',
        error: 'text-red-400',
        warning: 'text-yellow-400',
        info: 'text-blue-400'
      };
      return classes[this.type];
    },
    textClasses() {
      const classes = {
        success: 'text-green-800',
        error: 'text-red-800',
        warning: 'text-yellow-800',
        info: 'text-blue-800'
      };
      return classes[this.type];
    },
    icon() {
      const icons = {
        success: 'CheckCircleIcon',
        error: 'XCircleIcon',
        warning: 'ExclamationIcon',
        info: 'InformationCircleIcon'
      };
      return icons[this.type];
    }
  },
  watch: {
    show(newValue) {
      if (newValue && this.duration > 0) {
        this.startTimer();
      }
    }
  },
  methods: {
    close() {
      this.$emit('close');
    },
    startTimer() {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        this.close();
      }, this.duration);
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
  }
};
</script> 