<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" aria-hidden="true">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                Make a Payment
              </h3>
              <div class="mt-4">
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700">Expense Type</label>
                  <p class="mt-1 text-sm text-gray-900">{{ expense.expense_type }}</p>
                </div>
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700">Amount Due</label>
                  <p class="mt-1 text-sm text-gray-900">${{ expense.amount }}</p>
                </div>
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700">Due Date</label>
                  <p class="mt-1 text-sm text-gray-900">{{ formatDate(expense.due_date) }}</p>
                </div>
                <div class="mb-4">
                  <label for="payment_method" class="block text-sm font-medium text-gray-700">Payment Method</label>
                  <select
                    id="payment_method"
                    v-model="paymentMethod"
                    class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                  >
                    <option value="credit_card">Credit Card</option>
                    <option value="bank_transfer">Bank Transfer</option>
                    <option value="paypal">PayPal</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            type="button"
            @click="processPayment"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
            :disabled="processing"
          >
            {{ processing ? 'Processing...' : 'Pay Now' }}
          </button>
          <button
            type="button"
            @click="close"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PaymentModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    expense: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      paymentMethod: 'credit_card',
      processing: false
    };
  },
  methods: {
    formatDate(date) {
      return new Date(date).toLocaleDateString();
    },
    async processPayment() {
      this.processing = true;
      try {
        await this.$axios.post('/payments', {
          expense_id: this.expense.id,
          amount: this.expense.amount,
          payment_method: this.paymentMethod
        });
        
        this.$emit('payment-success');
        this.close();
      } catch (error) {
        this.$emit('payment-error', error.response?.data?.message || 'Payment failed');
      } finally {
        this.processing = false;
      }
    },
    close() {
      this.$emit('close');
    }
  }
};
</script> 