<template>
  <div class="min-h-screen bg-gray-100">
    <div class="flex">
      <!-- Sidebar -->
      <div class="w-64 bg-white shadow-lg h-screen fixed">
        <div class="p-4">
          <h2 class="text-xl font-bold text-gray-800 mb-6">{{ title }}</h2>
          <nav class="space-y-2">
            <slot name="sidebar"></slot>
          </nav>
        </div>
      </div>

      <!-- Main Content -->
      <div class="ml-64 flex-1 p-8">
        <div class="bg-white rounded-lg shadow p-6">
          <slot></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DashboardLayout',
  props: {
    title: {
      type: String,
      required: true
    }
  }
};
</script> 