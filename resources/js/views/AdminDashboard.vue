<template>
  <dashboard-layout title="Admin Dashboard">
    <template #sidebar>
      <router-link
        to="/admin/expenses"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Expenses
      </router-link>
      <router-link
        to="/admin/incomes"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Incomes
      </router-link>
      <router-link
        to="/admin/users"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Users
      </router-link>
      <router-link
        to="/admin/payments"
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Payments
      </router-link>
    </template>

    <div class="space-y-6">
      <!-- Overview Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-700">Total Expenses</h3>
          <p class="text-3xl font-bold text-indigo-600">${{ totalExpenses }}</p>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-700">Total Users</h3>
          <p class="text-3xl font-bold text-indigo-600">{{ totalUsers }}</p>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-700">Pending Payments</h3>
          <p class="text-3xl font-bold text-indigo-600">${{ pendingPayments }}</p>
        </div>
      </div>

      <!-- Recent Expenses -->
      <data-table
        title="Recent Expenses"
        :columns="expenseColumns"
        :items="recentExpenses"
      >
        <template #actions>
          <button class="text-indigo-600 hover:text-indigo-900">View</button>
        </template>
      </data-table>

      <!-- Recent Payments -->
      <data-table
        title="Recent Payments"
        :columns="paymentColumns"
        :items="recentPayments"
      >
        <template #actions>
          <button class="text-indigo-600 hover:text-indigo-900">View</button>
        </template>
      </data-table>
    </div>
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../components/DashboardLayout.vue';
import DataTable from '../components/DataTable.vue';

export default {
  components: {
    DashboardLayout,
    DataTable
  },
  data() {
    return {
      totalExpenses: 0,
      totalUsers: 0,
      pendingPayments: 0,
      recentExpenses: [],
      recentPayments: [],
      expenseColumns: [
        { key: 'expenseType.name', label: 'Type' },
        { key: 'amount', label: 'Amount' },
        { key: 'status', label: 'Status' }
      ],
      paymentColumns: [
        { key: 'user_name', label: 'User' },
        { key: 'amount', label: 'Amount' },
        { key: 'payment_date', label: 'Date' },
        { key: 'status', label: 'Status' }
      ]
    };
  },
  async created() {
    await this.loadDashboardData();
  },
  methods: {
    async loadDashboardData() {
      try {
        // Load summary data
        const summaryResponse = await this.$axios.get('/expenses/summary');
        this.totalExpenses = summaryResponse.data.total_amount || 0;

        // Load recent expenses
        const expensesResponse = await this.$axios.get('/expenses');
        this.recentExpenses = expensesResponse.data.slice(0, 5);

        // Load recent payments
        const paymentsResponse = await this.$axios.get('/payments');
        this.recentPayments = paymentsResponse.data.slice(0, 5);

        // Calculate pending payments
        this.pendingPayments = this.recentExpenses.reduce((total, expense) => {
          return total + (expense.status === 'pending' ? expense.amount : 0);
        }, 0);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      }
    }
  }
};
</script> 