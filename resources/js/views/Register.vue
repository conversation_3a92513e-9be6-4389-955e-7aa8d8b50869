<template>
  <div class="max-w-md mx-auto bg-white p-8 rounded shadow">
    <h2 class="text-2xl font-bold mb-6 text-center">Register</h2>
    <form @submit.prevent="register">
      <div class="mb-4">
        <label class="block mb-1">Name</label>
        <input v-model="name" type="text" class="w-full border rounded px-3 py-2" required />
      </div>
      <div class="mb-4">
        <label class="block mb-1">Email</label>
        <input v-model="email" type="email" class="w-full border rounded px-3 py-2" required />
      </div>
      <div class="mb-4">
        <label class="block mb-1">Apartment Number</label>
        <input v-model="apartment_number" type="text" class="w-full border rounded px-3 py-2" required />
      </div>
      <div class="mb-4">
        <label class="block mb-1">Password</label>
        <input v-model="password" type="password" class="w-full border rounded px-3 py-2" required />
      </div>
      <div class="mb-4">
        <label class="block mb-1">Confirm Password</label>
        <input v-model="password_confirmation" type="password" class="w-full border rounded px-3 py-2" required />
      </div>
      <div v-if="error" class="text-red-600 mb-4">{{ error }}</div>
      <button type="submit" class="w-full bg-indigo-600 text-white py-2 rounded hover:bg-indigo-700">Register</button>
      <div class="mt-4 text-center">
        <router-link to="/login" class="text-indigo-600 hover:underline">Login</router-link>
      </div>
    </form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      name: '',
      email: '',
      apartment_number: '',
      password: '',
      password_confirmation: '',
      error: '',
    };
  },
  methods: {
    async register() {
      this.error = '';
      try {
        const response = await this.$axios.post('/register', {
          name: this.name,
          email: this.email,
          apartment_number: this.apartment_number,
          password: this.password,
          password_confirmation: this.password_confirmation,
        });
        localStorage.setItem('token', response.data.token);
        localStorage.setItem('user', JSON.stringify(response.data.user));
        if (response.data.user.role === 'admin') {
          this.$router.push('/admin');
        } else {
          this.$router.push('/neighbor');
        }
      } catch (err) {
        this.error = err.response?.data?.message || 'Registration failed.';
      }
    },
  },
};
</script> 