<template>
  <dashboard-layout title="Create Expense">
    <template #sidebar>
      <router-link 
        to="/admin/expenses" 
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        All Expenses
      </router-link>
      <router-link 
        to="/admin/expenses/create" 
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Create Expense
      </router-link>
    </template>

    <div class="max-w-2xl mx-auto">
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-6">Create New Expense</h2>
        
        <expense-form
          @success="handleSuccess"
          @error="handleError"
          @cancel="handleCancel"
        />
      </div>
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../../components/DashboardLayout.vue';
import ExpenseForm from '../../components/ExpenseForm.vue';
import Notification from '../../components/Notification.vue';

export default {
  components: {
    DashboardLayout,
    ExpenseForm,
    Notification
  },
  data() {
    return {
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: ''
    };
  },
  methods: {
    handleSuccess() {
      this.showSuccess('Expense Created', 'The expense has been created successfully.');
      this.$router.push('/admin/expenses');
    },
    handleError(message) {
      this.showError('Creation Failed', message);
    },
    handleCancel() {
      this.$router.push('/admin/expenses');
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script> 