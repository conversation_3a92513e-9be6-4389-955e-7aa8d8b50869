<template>
  <dashboard-layout title="إدارة المصروفات">
    <template #sidebar>
      <router-link 
        to="/admin/expenses" 
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        جميع المصروفات
      </router-link>
      <router-link 
        to="/admin/expenses/create" 
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        إضافة مصروف
      </router-link>
    </template>

    <div class="space-y-6">
      <!-- Filters -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label for="type_filter" class="block text-sm font-medium text-gray-700">نوع المصروف</label>
            <select
              id="type_filter"
              v-model="filters.type"
              class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            >
              <option value="">جميع الأنواع</option>
              <option value="building_services">خدمات عمارة</option>
              <option value="building_electricity">كهرباء عمارة</option>
              <option value="personal_electricity">كهرباء منزلي</option>
              <option value="water">مياه</option>
              <option value="other">أخرى</option>
            </select>
          </div>
          <div>
            <label for="month_filter" class="block text-sm font-medium text-gray-700">الشهر</label>
            <select
              id="month_filter"
              v-model="filters.month"
              class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            >
              <option value="">جميع الشهور</option>
              <option value="01">يناير</option>
              <option value="02">فبراير</option>
              <option value="03">مارس</option>
              <option value="04">أبريل</option>
              <option value="05">مايو</option>
              <option value="06">يونيو</option>
              <option value="07">يوليو</option>
              <option value="08">أغسطس</option>
              <option value="09">سبتمبر</option>
              <option value="10">أكتوبر</option>
              <option value="11">نوفمبر</option>
              <option value="12">ديسمبر</option>
            </select>
          </div>
          <div>
            <label for="year_filter" class="block text-sm font-medium text-gray-700">السنة</label>
            <input
              type="number"
              id="year_filter"
              v-model="filters.year"
              class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
              min="2023"
              max="2025"
            />
          </div>
          <div>
            <label for="status_filter" class="block text-sm font-medium text-gray-700">الحالة</label>
            <select
              id="status_filter"
              v-model="filters.status"
              class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
            >
              <option value="">جميع الحالات</option>
              <option value="pending">معلق</option>
              <option value="paid">مدفوع</option>
              <option value="overdue">متأخر</option>
            </select>
          </div>
        </div>
        <div class="mt-4 flex justify-between">
          <button
            @click="generateMonthlyExpenses"
            :disabled="generatingExpenses"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400"
          >
            {{ generatingExpenses ? 'Generating...' : 'Generate Monthly Expenses (70 ILS)' }}
          </button>
          <button
            @click="applyFilters"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Apply Filters
          </button>
        </div>
      </div>

      <!-- Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-700">مجموع المصروفات</h3>
          <p class="text-3xl font-bold text-indigo-600">${{ totalExpenses }}</p>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-700">المدفوعات</h3>
          <p class="text-3xl font-bold text-green-600">${{ totalPaid }}</p>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-700">المتبقي</h3>
          <p class="text-3xl font-bold text-red-600">${{ totalRemaining }}</p>
        </div>
      </div>

      <!-- Expenses Table -->
      <data-table
        title="المصروفات"
        :columns="columns"
        :items="expenses"
        :loading="loading"
      >
        <template #actions="{ item }">
          <div class="flex space-x-2">
            <button 
              @click="editExpense(item)"
              class="text-indigo-600 hover:text-indigo-900"
            >
              تعديل
            </button>
            <button 
              @click="deleteExpense(item)"
              class="text-red-600 hover:text-red-900"
            >
              حذف
            </button>
          </div>
        </template>
      </data-table>

      <!-- Pagination -->
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-700">
          عرض {{ pagination.from }} إلى {{ pagination.to }} من {{ pagination.total }} نتيجة
        </div>
        <div class="flex space-x-2">
          <button
            @click="previousPage"
            :disabled="pagination.currentPage === 1"
            class="px-3 py-1 border rounded-md text-sm"
            :class="pagination.currentPage === 1 ? 'bg-gray-100 text-gray-400' : 'bg-white text-gray-700 hover:bg-gray-50'"
          >
            السابق
          </button>
          <button
            @click="nextPage"
            :disabled="pagination.currentPage === pagination.lastPage"
            class="px-3 py-1 border rounded-md text-sm"
            :class="pagination.currentPage === pagination.lastPage ? 'bg-gray-100 text-gray-400' : 'bg-white text-gray-700 hover:bg-gray-50'"
          >
            التالي
          </button>
        </div>
      </div>
    </div>

    <!-- Edit Modal -->
    <div v-if="showEditModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
              تعديل المصروف
            </h3>
            <expense-form
              :expense="selectedExpense"
              :is-edit="true"
              @success="handleEditSuccess"
              @error="handleEditError"
              @cancel="closeEditModal"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../../components/DashboardLayout.vue';
import DataTable from '../../components/DataTable.vue';
import ExpenseForm from '../../components/ExpenseForm.vue';
import Notification from '../../components/Notification.vue';

export default {
  components: {
    DashboardLayout,
    DataTable,
    ExpenseForm,
    Notification
  },
  data() {
    return {
      loading: false,
      generatingExpenses: false,
      expenses: [],
      selectedExpense: null,
      showEditModal: false,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      filters: {
        type: '',
        month: '',
        year: new Date().getFullYear(),
        status: ''
      },
      pagination: {
        currentPage: 1,
        lastPage: 1,
        from: 0,
        to: 0,
        total: 0
      },
      columns: [
        { key: 'expense_type.name', label: 'Type' },
        { key: 'user.name', label: 'Neighbor' },
        { key: 'user.apartment_number', label: 'Apartment' },
        { key: 'month', label: 'Month' },
        { key: 'year', label: 'Year' },
        { key: 'amount', label: 'Amount' },
        { key: 'due_date', label: 'Due Date' },
        { key: 'status', label: 'Status' },
        { key: 'is_automatic', label: 'Auto' },
        { key: 'notes', label: 'Notes' }
      ]
    };
  },
  computed: {
    totalExpenses() {
      return this.expenses.reduce((total, expense) => total + parseFloat(expense.amount), 0);
    },
    totalPaid() {
      return this.expenses
        .filter(expense => expense.status === 'paid')
        .reduce((total, expense) => total + parseFloat(expense.amount), 0);
    },
    totalRemaining() {
      return this.totalExpenses - this.totalPaid;
    }
  },
  created() {
    this.loadExpenses();
  },
  methods: {
    async loadExpenses() {
      this.loading = true;
      try {
        const response = await this.$axios.get('/expenses', {
          params: {
            page: this.pagination.currentPage,
            ...this.filters
          }
        });
        
        this.expenses = response.data.data;
        this.pagination = {
          currentPage: response.data.current_page,
          lastPage: response.data.last_page,
          from: response.data.from,
          to: response.data.to,
          total: response.data.total
        };
      } catch (error) {
        this.showError('Error loading expenses');
      } finally {
        this.loading = false;
      }
    },
    applyFilters() {
      this.pagination.currentPage = 1;
      this.loadExpenses();
    },
    previousPage() {
      if (this.pagination.currentPage > 1) {
        this.pagination.currentPage--;
        this.loadExpenses();
      }
    },
    nextPage() {
      if (this.pagination.currentPage < this.pagination.lastPage) {
        this.pagination.currentPage++;
        this.loadExpenses();
      }
    },
    editExpense(expense) {
      this.selectedExpense = expense;
      this.showEditModal = true;
    },
    async deleteExpense(expense) {
      if (confirm('Are you sure you want to delete this expense?')) {
        try {
          await this.$axios.delete(`/expenses/${expense.id}`);
          this.showSuccess('Deleted', 'Expense deleted successfully');
          this.loadExpenses();
        } catch (error) {
          this.showError('Delete Failed', 'Failed to delete expense');
        }
      }
    },
    handleEditSuccess() {
      this.showSuccess('Updated', 'Expense updated successfully');
      this.closeEditModal();
      this.loadExpenses();
    },
    handleEditError(message) {
      this.showError('Update Failed', message);
    },
    closeEditModal() {
      this.showEditModal = false;
      this.selectedExpense = null;
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    },
    async generateMonthlyExpenses() {
      const month = this.filters.month || new Date().getMonth() + 1;
      const year = this.filters.year || new Date().getFullYear();

      const formattedMonth = month.toString().padStart(2, '0');

      if (confirm(`Generate monthly expenses (70 ILS) for all neighbors for ${formattedMonth}/${year}?`)) {
        this.generatingExpenses = true;
        try {
          const response = await this.$axios.post('/expenses/generate-monthly', {
            month: formattedMonth,
            year: year.toString()
          });

          this.showSuccess('Generated', 'Monthly expenses generated successfully');
          this.loadExpenses();
        } catch (error) {
          this.showError('Generation Failed', error.response?.data?.message || 'Failed to generate monthly expenses');
        } finally {
          this.generatingExpenses = false;
        }
      }
    }
  }
};
</script> 