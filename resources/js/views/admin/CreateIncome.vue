<template>
  <dashboard-layout title="Record Income">
    <template #sidebar>
      <router-link 
        to="/admin/incomes" 
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        All Incomes
      </router-link>
      <router-link 
        to="/admin/incomes/create" 
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Record Income
      </router-link>
      <router-link 
        to="/admin/expenses" 
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        Expenses
      </router-link>
    </template>

    <div class="max-w-2xl mx-auto">
      <div class="bg-white p-6 rounded-lg shadow-sm">
        <income-form
          @success="handleSuccess"
          @error="handleError"
          @cancel="handleCancel"
        />
      </div>
    </div>

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../../components/DashboardLayout.vue';
import IncomeForm from '../../components/IncomeForm.vue';
import Notification from '../../components/Notification.vue';

export default {
  components: {
    DashboardLayout,
    IncomeForm,
    Notification
  },
  data() {
    return {
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: ''
    };
  },
  methods: {
    handleSuccess() {
      this.showSuccess('Income Recorded', 'The income has been recorded successfully.');
      this.$router.push('/admin/incomes');
    },
    handleError(message) {
      this.showError('Recording Failed', message);
    },
    handleCancel() {
      this.$router.push('/admin/incomes');
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script>
