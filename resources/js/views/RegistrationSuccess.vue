<template>
  <div class="min-h-screen bg-gray-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div class="text-center">
        <!-- Success Icon -->
        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
          <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
        
        <!-- Success Message -->
        <h2 class="text-3xl font-bold text-gray-900 mb-4">
          Registration Successful!
        </h2>
        
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
          <p class="text-gray-600 mb-4">
            Welcome to our community, <span class="font-semibold text-gray-900">{{ user?.name }}</span>!
          </p>
          
          <div class="text-left space-y-2 text-sm text-gray-600">
            <div class="flex justify-between">
              <span class="font-medium">Email:</span>
              <span>{{ user?.email }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium">Apartment:</span>
              <span>{{ user?.apartment_number }}</span>
            </div>
            <div class="flex justify-between">
              <span class="font-medium">Role:</span>
              <span class="capitalize">{{ user?.role || 'Neighbor' }}</span>
            </div>
          </div>
        </div>
        
        <!-- Success Details -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-800">
                What's Next?
              </h3>
              <div class="mt-2 text-sm text-blue-700">
                <ul class="list-disc list-inside space-y-1">
                  <li>Your account has been created successfully</li>
                  <li>You can now access your dashboard</li>
                  <li v-if="user?.role === 'admin'">Manage expenses and view reports</li>
                  <li v-else>View expenses and make payments</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="space-y-3">
          <button
            @click="goToDashboard"
            class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
          >
            Continue to Dashboard
            <svg class="ml-2 -mr-1 w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </button>
          
          <button
            @click="logout"
            class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out"
          >
            Logout
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RegistrationSuccess',
  data() {
    return {
      user: null
    };
  },
  created() {
    // Get user data from localStorage
    this.user = JSON.parse(localStorage.getItem('user') || 'null');
    
    // If no user data, redirect to login
    if (!this.user) {
      this.$router.push('/login');
    }
  },
  methods: {
    goToDashboard() {
      if (this.user?.role === 'admin') {
        this.$router.push('/admin');
      } else {
        this.$router.push('/neighbor');
      }
    },
    async logout() {
      try {
        await this.$axios.post('/logout');
      } catch (error) {
        console.error('Logout error:', error);
      } finally {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        this.$router.push('/login');
      }
    }
  }
};
</script>
