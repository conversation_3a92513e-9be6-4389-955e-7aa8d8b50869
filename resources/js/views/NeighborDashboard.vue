<template>
  <dashboard-layout title="Neighbor Dashboard">
    <template #sidebar>
      <router-link 
        to="/neighbor/expenses" 
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        My Expenses
      </router-link>
      <router-link 
        to="/neighbor/payments" 
        class="block px-4 py-2 text-gray-600 hover:bg-gray-100 rounded"
        active-class="bg-indigo-50 text-indigo-600"
      >
        My Payments
      </router-link>
    </template>

    <div class="space-y-6">
      <!-- Overview Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-700">Total Due</h3>
          <p class="text-3xl font-bold text-indigo-600">${{ totalDue }}</p>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-700">Paid This Month</h3>
          <p class="text-3xl font-bold text-indigo-600">${{ paidThisMonth }}</p>
        </div>
        <div class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold text-gray-700">Next Payment Due</h3>
          <p class="text-3xl font-bold text-indigo-600">${{ nextPaymentDue }}</p>
        </div>
      </div>

      <!-- Upcoming Expenses -->
      <data-table
        title="Upcoming Expenses"
        :columns="expenseColumns"
        :items="upcomingExpenses"
      >
        <template #actions="{ item }">
          <button 
            @click="openPaymentModal(item)"
            class="text-indigo-600 hover:text-indigo-900"
          >
            Pay Now
          </button>
        </template>
      </data-table>

      <!-- Recent Payments -->
      <data-table
        title="Recent Payments"
        :columns="paymentColumns"
        :items="recentPayments"
      >
        <template #actions="{ item }">
          <button 
            @click="viewReceipt(item)"
            class="text-indigo-600 hover:text-indigo-900"
          >
            View Receipt
          </button>
        </template>
      </data-table>
    </div>

    <!-- Payment Modal -->
    <payment-modal
      :show="showPaymentModal"
      :expense="selectedExpense"
      @close="closePaymentModal"
      @payment-success="handlePaymentSuccess"
      @payment-error="handlePaymentError"
    />

    <!-- Notifications -->
    <notification
      :show="showNotification"
      :type="notificationType"
      :title="notificationTitle"
      :message="notificationMessage"
      @close="closeNotification"
    />
  </dashboard-layout>
</template>

<script>
import DashboardLayout from '../components/DashboardLayout.vue';
import DataTable from '../components/DataTable.vue';
import PaymentModal from '../components/PaymentModal.vue';
import Notification from '../components/Notification.vue';

export default {
  components: {
    DashboardLayout,
    DataTable,
    PaymentModal,
    Notification
  },
  data() {
    return {
      totalDue: 0,
      paidThisMonth: 0,
      nextPaymentDue: 0,
      upcomingExpenses: [],
      recentPayments: [],
      showPaymentModal: false,
      selectedExpense: null,
      showNotification: false,
      notificationType: 'info',
      notificationTitle: '',
      notificationMessage: '',
      expenseColumns: [
        { key: 'expense_type', label: 'Type' },
        { key: 'amount', label: 'Amount' },
        { key: 'due_date', label: 'Due Date' },
        { key: 'status', label: 'Status' }
      ],
      paymentColumns: [
        { key: 'expense_type', label: 'Expense Type' },
        { key: 'amount', label: 'Amount' },
        { key: 'payment_date', label: 'Date' },
        { key: 'status', label: 'Status' }
      ]
    };
  },
  async created() {
    await this.loadDashboardData();
  },
  methods: {
    async loadDashboardData() {
      try {
        // Load user's expenses
        const expensesResponse = await this.$axios.get('/expenses/my-expenses');
        this.upcomingExpenses = expensesResponse.data.filter(expense => 
          expense.status === 'pending'
        ).slice(0, 5);

        // Load user's payments
        const paymentsResponse = await this.$axios.get('/payments/my-payments');
        this.recentPayments = paymentsResponse.data.slice(0, 5);

        // Calculate totals
        this.totalDue = this.upcomingExpenses.reduce((total, expense) => 
          total + expense.amount, 0
        );

        this.paidThisMonth = this.recentPayments
          .filter(payment => {
            const paymentDate = new Date(payment.payment_date);
            const now = new Date();
            return paymentDate.getMonth() === now.getMonth() &&
                   paymentDate.getFullYear() === now.getFullYear();
          })
          .reduce((total, payment) => total + payment.amount, 0);

        // Get next payment due
        const nextExpense = this.upcomingExpenses[0];
        this.nextPaymentDue = nextExpense ? nextExpense.amount : 0;
      } catch (error) {
        this.showError('Error loading dashboard data');
      }
    },
    openPaymentModal(expense) {
      this.selectedExpense = expense;
      this.showPaymentModal = true;
    },
    closePaymentModal() {
      this.showPaymentModal = false;
      this.selectedExpense = null;
    },
    handlePaymentSuccess() {
      this.showSuccess('Payment Successful', 'Your payment has been processed successfully.');
      this.loadDashboardData();
    },
    handlePaymentError(message) {
      this.showError('Payment Failed', message);
    },
    viewReceipt(payment) {
      // Implement receipt viewing logic
      window.open(`/payments/${payment.id}/receipt`, '_blank');
    },
    showSuccess(title, message) {
      this.notificationType = 'success';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    showError(title, message) {
      this.notificationType = 'error';
      this.notificationTitle = title;
      this.notificationMessage = message;
      this.showNotification = true;
    },
    closeNotification() {
      this.showNotification = false;
    }
  }
};
</script> 