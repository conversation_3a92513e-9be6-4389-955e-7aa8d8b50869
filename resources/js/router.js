import { createRouter, createWebHistory } from 'vue-router';
import Users from './components/Admin/Users.vue';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('./views/Login.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('./views/Register.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/registration-success',
    name: 'RegistrationSuccess',
    component: () => import('./views/RegistrationSuccess.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/admin',
    name: 'AdminDashboard',
    component: () => import('./views/AdminDashboard.vue'),
    meta: { requiresAuth: true, role: 'admin' }
  },
  {
    path: '/neighbor',
    name: 'NeighborDashboard',
    component: () => import('./views/NeighborDashboard.vue'),
    meta: { requiresAuth: true, role: 'neighbor' }
  },
  {
    path: '/admin/users',
    name: 'admin.users',
    component: Users,
    meta: { requiresAuth: true }
  },
  {
    path: '/',
    redirect: '/login'
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// Navigation Guard
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || 'null');

  console.log('Router guard - navigating to:', to.name, to.path);
  console.log('Router guard - user:', user);
  console.log('Router guard - token exists:', !!token);

  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    if (!token) {
      // No token, redirect to login
      console.log('Router guard - no token, redirecting to login');
      next({ name: 'Login' });
      return;
    }

    // Check role-based access
    if (to.meta.role && user?.role !== to.meta.role) {
      // Wrong role, redirect to appropriate dashboard
      console.log('Router guard - wrong role, redirecting. Required:', to.meta.role, 'User role:', user?.role);
      next({ name: user?.role === 'admin' ? 'AdminDashboard' : 'NeighborDashboard' });
      return;
    }
  }

  // Check if route requires guest (not logged in)
  if (to.meta.requiresGuest) {
    if (token) {
      // User is logged in, redirect to appropriate dashboard
      console.log('Router guard - user logged in, redirecting from guest route');
      next({ name: user?.role === 'admin' ? 'AdminDashboard' : 'NeighborDashboard' });
      return;
    }
  }

  console.log('Router guard - allowing navigation');
  next();
});

export default router; 