import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('./views/Login.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('./views/Register.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/registration-success',
    name: 'RegistrationSuccess',
    component: () => import('./views/RegistrationSuccess.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/admin',
    name: 'AdminDashboard',
    component: () => import('./views/AdminDashboard.vue'),
    meta: { requiresAuth: true, role: 'admin' }
  },
  {
    path: '/neighbor',
    name: 'NeighborDashboard',
    component: () => import('./views/NeighborDashboard.vue'),
    meta: { requiresAuth: true, role: 'neighbor' }
  },
  {
    path: '/',
    redirect: '/login'
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// Navigation Guard
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || 'null');

  // Check if route requires authentication
  if (to.meta.requiresAuth) {
    if (!token) {
      // No token, redirect to login
      next({ name: 'Login' });
      return;
    }

    // Check role-based access
    if (to.meta.role && user?.role !== to.meta.role) {
      // Wrong role, redirect to appropriate dashboard
      next({ name: user?.role === 'admin' ? 'AdminDashboard' : 'NeighborDashboard' });
      return;
    }
  }

  // Check if route requires guest (not logged in)
  if (to.meta.requiresGuest) {
    if (token) {
      // User is logged in, redirect to appropriate dashboard
      next({ name: user?.role === 'admin' ? 'AdminDashboard' : 'NeighborDashboard' });
      return;
    }
  }

  next();
});

export default router; 