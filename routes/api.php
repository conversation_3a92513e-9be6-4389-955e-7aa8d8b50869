<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\ExpenseTypeController;
use App\Http\Controllers\API\ExpenseController;
use App\Http\Controllers\API\PaymentController;
use App\Http\Controllers\Admin\UserController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'user']);

    // Expense Types Routes
    Route::apiResource('expense-types', ExpenseTypeController::class);

    // Expenses Routes
    Route::apiResource('expenses', ExpenseController::class);
    Route::get('expenses/monthly', [ExpenseController::class, 'getMonthlyExpenses']);
    Route::get('expenses/summary', [ExpenseController::class, 'getExpenseSummary']);

    // Payments Routes
    Route::apiResource('payments', PaymentController::class);
    Route::get('payments/user', [PaymentController::class, 'getUserPayments']);
    Route::get('payments/expense', [PaymentController::class, 'getExpensePayments']);
    Route::get('payments/summary', [PaymentController::class, 'getPaymentSummary']);

    // Admin routes
    Route::prefix('admin')->group(function () {
        Route::get('/users', [UserController::class, 'index']);
        Route::post('/users', [UserController::class, 'store']);
        Route::put('/users/{user}', [UserController::class, 'update']);
        Route::delete('/users/{user}', [UserController::class, 'destroy']);
    });
}); 