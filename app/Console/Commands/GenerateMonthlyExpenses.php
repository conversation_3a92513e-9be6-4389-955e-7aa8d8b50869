<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Expense;
use App\Models\ExpenseType;
use Illuminate\Console\Command;
use Carbon\Carbon;

class GenerateMonthlyExpenses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'expenses:generate-monthly {--month=} {--year=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate monthly expenses for all neighbors (70 ILS each)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $month = $this->option('month') ?: Carbon::now()->format('m');
        $year = $this->option('year') ?: Carbon::now()->format('Y');

        // Get or create the monthly maintenance expense type
        $expenseType = ExpenseType::firstOrCreate(
            ['name' => 'Monthly Maintenance'],
            ['description' => 'Monthly maintenance fee for all neighbors']
        );

        // Get all neighbors (non-admin users)
        $neighbors = User::where('role', 'neighbor')->get();

        $created = 0;
        $skipped = 0;

        foreach ($neighbors as $neighbor) {
            // Check if expense already exists for this neighbor, month, and year
            $existingExpense = Expense::where('user_id', $neighbor->id)
                ->where('expense_type_id', $expenseType->id)
                ->where('month', $month)
                ->where('year', $year)
                ->where('is_automatic', true)
                ->first();

            if ($existingExpense) {
                $skipped++;
                continue;
            }

            // Create the monthly expense
            Expense::create([
                'expense_type_id' => $expenseType->id,
                'user_id' => $neighbor->id,
                'amount' => 70.00,
                'due_date' => Carbon::createFromDate($year, $month, 1)->endOfMonth(),
                'month' => $month,
                'year' => $year,
                'notes' => 'Monthly maintenance fee',
                'status' => 'pending',
                'is_automatic' => true,
            ]);

            $created++;
        }

        $this->info("Monthly expenses generated for {$month}/{$year}:");
        $this->info("Created: {$created} expenses");
        $this->info("Skipped: {$skipped} expenses (already exist)");

        return Command::SUCCESS;
    }
}
