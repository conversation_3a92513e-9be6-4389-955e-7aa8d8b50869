<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class UserController extends Controller
{
    public function index(Request $request)
    {
        $users = User::latest()->paginate(10);

        // Return JSON for API requests
        if ($request->expectsJson()) {
            return response()->json($users);
        }

        // Return view for web requests
        return view('admin.users.index', compact('users'));
    }

    public function create()
    {
        return view('admin.users.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Password::defaults()],
            'apartment_number' => ['required', 'string', 'max:10'],
            'role' => ['required', 'string', 'in:admin,neighbor'],
        ]);

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'apartment_number' => $validated['apartment_number'],
            'role' => $validated['role'],
        ]);

        // Return JSON for API requests
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'User created successfully.',
                'user' => $user
            ], 201);
        }

        // Return redirect for web requests
        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }

    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }

    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'password' => ['nullable', 'confirmed', Password::defaults()],
            'apartment_number' => ['required', 'string', 'max:10'],
            'role' => ['required', 'string', 'in:admin,neighbor'],
        ]);

        $user->name = $validated['name'];
        $user->email = $validated['email'];
        $user->apartment_number = $validated['apartment_number'];
        $user->role = $validated['role'];

        if ($request->filled('password')) {
            $user->password = Hash::make($validated['password']);
        }

        $user->save();

        // Return JSON for API requests
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'User updated successfully.',
                'user' => $user
            ]);
        }

        // Return redirect for web requests
        return redirect()->route('admin.users.index')
            ->with('success', 'User updated successfully.');
    }

    public function destroy(Request $request, User $user)
    {
        $user->delete();

        // Return JSON for API requests
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'User deleted successfully.'
            ]);
        }

        // Return redirect for web requests
        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }
} 