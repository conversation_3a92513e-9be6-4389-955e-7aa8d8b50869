<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\ExpenseType;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ExpenseTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $expenseTypes = ExpenseType::all();
        return response()->json($expenseTypes);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        $expenseType = ExpenseType::create($validated);
        return response()->json($expenseType, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(ExpenseType $expenseType): JsonResponse
    {
        return response()->json($expenseType);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ExpenseType $expenseType): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);

        $expenseType->update($validated);
        return response()->json($expenseType);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ExpenseType $expenseType): JsonResponse
    {
        $expenseType->delete();
        return response()->json(null, 204);
    }
}
