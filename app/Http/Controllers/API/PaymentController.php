<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PaymentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $payments = Payment::with(['user', 'expense'])->get();
        return response()->json($payments);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'expense_id' => 'required|exists:expenses,id',
            'amount' => 'required|numeric|min:0',
            'payment_date' => 'required|date',
            'payment_method' => 'required|in:cash,bank_transfer,check',
            'reference_number' => 'nullable|string',
            'notes' => 'nullable|string',
            'status' => 'required|in:pending,completed,failed',
        ]);

        $payment = Payment::create($validated);
        return response()->json($payment, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Payment $payment): JsonResponse
    {
        $payment->load(['user', 'expense']);
        return response()->json($payment);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Payment $payment): JsonResponse
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'expense_id' => 'required|exists:expenses,id',
            'amount' => 'required|numeric|min:0',
            'payment_date' => 'required|date',
            'payment_method' => 'required|in:cash,bank_transfer,check',
            'reference_number' => 'nullable|string',
            'notes' => 'nullable|string',
            'status' => 'required|in:pending,completed,failed',
        ]);

        $payment->update($validated);
        return response()->json($payment);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Payment $payment): JsonResponse
    {
        $payment->delete();
        return response()->json(null, 204);
    }

    public function getUserPayments(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $payments = Payment::with(['expense'])
            ->where('user_id', $validated['user_id'])
            ->get();

        return response()->json($payments);
    }

    public function getExpensePayments(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'expense_id' => 'required|exists:expenses,id',
        ]);

        $payments = Payment::with(['user'])
            ->where('expense_id', $validated['expense_id'])
            ->get();

        return response()->json($payments);
    }

    public function getPaymentSummary(): JsonResponse
    {
        $summary = Payment::selectRaw('
            payment_method,
            status,
            COUNT(*) as total_payments,
            SUM(amount) as total_amount
        ')
        ->groupBy('payment_method', 'status')
        ->get();

        return response()->json($summary);
    }
}
