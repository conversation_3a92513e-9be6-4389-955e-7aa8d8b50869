<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Expense;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ExpenseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Expense::with(['expenseType', 'user', 'payments']);

        // Apply filters
        if ($request->filled('type')) {
            $query->whereHas('expenseType', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->type . '%');
            });
        }

        if ($request->filled('month')) {
            $query->where('month', $request->month);
        }

        if ($request->filled('year')) {
            $query->where('year', $request->year);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('is_automatic')) {
            $query->where('is_automatic', $request->is_automatic);
        }

        // Order by latest
        $query->latest();

        // Paginate results
        $expenses = $query->paginate(10);

        return response()->json($expenses);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'expense_type_id' => 'required|exists:expense_types,id',
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0',
            'month' => 'required|string',
            'year' => 'required|string',
            'notes' => 'nullable|string',
            'status' => 'required|in:pending,paid,overdue',
            'is_automatic' => 'boolean',
        ]);

        // Auto-generate due_date as end of month
        $validated['due_date'] = \Carbon\Carbon::createFromDate($validated['year'], $validated['month'], 1)->endOfMonth();

        $expense = Expense::create($validated);
        $expense->load(['expenseType', 'user']);
        return response()->json($expense, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Expense $expense): JsonResponse
    {
        $expense->load(['expenseType', 'user', 'payments']);
        return response()->json($expense);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Expense $expense): JsonResponse
    {
        $validated = $request->validate([
            'expense_type_id' => 'required|exists:expense_types,id',
            'user_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0',
            'month' => 'required|string',
            'year' => 'required|string',
            'notes' => 'nullable|string',
            'status' => 'required|in:pending,paid,overdue',
            'is_automatic' => 'boolean',
        ]);

        // Auto-generate due_date as end of month if not provided
        if (!isset($validated['due_date'])) {
            $validated['due_date'] = \Carbon\Carbon::createFromDate($validated['year'], $validated['month'], 1)->endOfMonth();
        }

        $expense->update($validated);
        $expense->load(['expenseType', 'user']);
        return response()->json($expense);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Expense $expense): JsonResponse
    {
        $expense->delete();
        return response()->json(null, 204);
    }

    public function getMonthlyExpenses(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'month' => 'required|string',
            'year' => 'required|string',
        ]);

        $expenses = Expense::with(['expenseType', 'payments'])
            ->where('month', $validated['month'])
            ->where('year', $validated['year'])
            ->get();

        return response()->json($expenses);
    }

    public function getExpenseSummary(): JsonResponse
    {
        $summary = Expense::selectRaw('
            expense_type_id,
            month,
            year,
            SUM(amount) as total_amount,
            COUNT(*) as total_expenses,
            SUM(CASE WHEN status = "paid" THEN 1 ELSE 0 END) as paid_expenses
        ')
        ->groupBy('expense_type_id', 'month', 'year')
        ->with('expenseType')
        ->get();

        return response()->json($summary);
    }

    public function generateMonthlyExpenses(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'month' => 'required|string|size:2',
            'year' => 'required|string|size:4',
        ]);

        // Call the artisan command
        \Artisan::call('expenses:generate-monthly', [
            '--month' => $validated['month'],
            '--year' => $validated['year'],
        ]);

        $output = \Artisan::output();

        return response()->json([
            'message' => 'Monthly expenses generation completed',
            'output' => $output
        ]);
    }
}
