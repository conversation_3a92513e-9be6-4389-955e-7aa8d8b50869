<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Expense;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ExpenseController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Expense::with(['expenseType', 'payments']);

        // Apply filters
        if ($request->filled('type')) {
            $query->whereHas('expenseType', function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->type . '%');
            });
        }

        if ($request->filled('month')) {
            $query->where('month', $request->month);
        }

        if ($request->filled('year')) {
            $query->where('year', $request->year);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Order by latest
        $query->latest();

        // Paginate results
        $expenses = $query->paginate(10);

        return response()->json($expenses);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'expense_type_id' => 'required|exists:expense_types,id',
            'amount' => 'required|numeric|min:0',
            'due_date' => 'required|date',
            'month' => 'required|string',
            'year' => 'required|string',
            'notes' => 'nullable|string',
            'status' => 'required|in:pending,paid,overdue',
        ]);

        $expense = Expense::create($validated);
        return response()->json($expense, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(Expense $expense): JsonResponse
    {
        $expense->load(['expenseType', 'payments']);
        return response()->json($expense);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Expense $expense): JsonResponse
    {
        $validated = $request->validate([
            'expense_type_id' => 'required|exists:expense_types,id',
            'amount' => 'required|numeric|min:0',
            'due_date' => 'required|date',
            'month' => 'required|string',
            'year' => 'required|string',
            'notes' => 'nullable|string',
            'status' => 'required|in:pending,paid,overdue',
        ]);

        $expense->update($validated);
        return response()->json($expense);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Expense $expense): JsonResponse
    {
        $expense->delete();
        return response()->json(null, 204);
    }

    public function getMonthlyExpenses(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'month' => 'required|string',
            'year' => 'required|string',
        ]);

        $expenses = Expense::with(['expenseType', 'payments'])
            ->where('month', $validated['month'])
            ->where('year', $validated['year'])
            ->get();

        return response()->json($expenses);
    }

    public function getExpenseSummary(): JsonResponse
    {
        $summary = Expense::selectRaw('
            expense_type_id,
            month,
            year,
            SUM(amount) as total_amount,
            COUNT(*) as total_expenses,
            SUM(CASE WHEN status = "paid" THEN 1 ELSE 0 END) as paid_expenses
        ')
        ->groupBy('expense_type_id', 'month', 'year')
        ->with('expenseType')
        ->get();

        return response()->json($summary);
    }
}
